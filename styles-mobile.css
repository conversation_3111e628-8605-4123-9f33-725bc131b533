/* 手机端FAQ+对话式设计 - 合并版CSS */

:root {
  /* 扩展紫色主题色板 */
  --primary-50: #faf5ff;
  --primary-100: #f3e8ff;
  --primary-200: #e9d5ff;
  --primary-300: #d8b4fe;
  --primary-400: #c084fc;
  --primary-500: #a855f7;
  --primary-600: #9333ea;
  --primary-700: #7c3aed;
  --primary-800: #6b21a8;
  --primary-900: #581c87;

  /* 品牌颜色系统 */
  --primary-color: var(--primary-500);
  --primary-dark: var(--primary-600);
  --primary-light: var(--primary-100);
  --secondary-color: #b83dba;
  --accent-color: var(--primary-600);

  /* 语义颜色 - 亮色主题 */
  --text-primary: #111827;
  --text-secondary: #6b7280;
  --text-tertiary: #9ca3af;
  --text-inverse: #ffffff;
  --background-color: #ffffff;
  --background-secondary: #f9fafb;
  --surface-color: #ffffff;
  --surface-elevated: #ffffff;
  --border-color: #e5e7eb;
  --border-light: #f3f4f6;
  --border-focus: var(--primary-500);
  --shadow-color: rgba(0, 0, 0, 0.1);
  --overlay-color: rgba(0, 0, 0, 0.5);

  /* 状态颜色 */
  --success-color: #10b981;
  --success-light: #d1fae5;
  --warning-color: #f59e0b;
  --warning-light: #fef3c7;
  --danger-color: #ef4444;
  --danger-light: #fee2e2;
  --info-color: #3b82f6;
  --info-light: #dbeafe;
  
  /* 阴影系统 */
  --shadow-xs: 0 1px 2px 0 var(--shadow-color);
  --shadow-sm: 0 1px 3px 0 var(--shadow-color), 0 1px 2px -1px var(--shadow-color);
  --shadow-md: 0 4px 6px -1px var(--shadow-color), 0 2px 4px -2px var(--shadow-color);
  --shadow-lg: 0 10px 15px -3px var(--shadow-color), 0 4px 6px -4px var(--shadow-color);
  --shadow-xl: 0 20px 25px -5px var(--shadow-color), 0 8px 10px -6px var(--shadow-color);
  --shadow-2xl: 0 25px 50px -12px var(--shadow-color);
  --shadow-inner: inset 0 2px 4px 0 var(--shadow-color);

  /* 圆角系统 */
  --radius-xs: 2px;
  --radius-sm: 4px;
  --radius: 6px;
  --radius-md: 8px;
  --radius-lg: 12px;
  --radius-xl: 16px;
  --radius-2xl: 24px;
  --radius-full: 9999px;

  /* 间距系统 */
  --space-xs: 4px;
  --space-sm: 8px;
  --space-md: 12px;
  --space-lg: 16px;
  --space-xl: 20px;
  --space-2xl: 24px;
  --space-3xl: 32px;
  --space-4xl: 40px;

  /* 字体系统 */
  --font-xs: 12px;
  --font-sm: 14px;
  --font-base: 16px;
  --font-lg: 18px;
  --font-xl: 20px;
  --font-2xl: 24px;
  --font-3xl: 30px;

  /* 手机端尺寸系统 */
  --header-height: 120px; /* 增加高度以适应两层布局 */
  --bottom-nav-height: 68px;
  --fab-size: 56px;
  --emergency-size: 48px;
  --touch-target: 48px;
  
  /* 安全区域 */
  --safe-top: env(safe-area-inset-top);
  --safe-bottom: env(safe-area-inset-bottom);
  --safe-left: env(safe-area-inset-left);
  --safe-right: env(safe-area-inset-right);
  
  /* 间距系统 */
  --space-xs: 4px;
  --space-sm: 8px;
  --space-md: 12px;
  --space-lg: 16px;
  --space-xl: 20px;
  --space-2xl: 24px;
  
  /* 圆角系统 */
  --radius-sm: 4px;
  --radius: 8px;
  --radius-lg: 12px;
  --radius-xl: 16px;
  --radius-full: 50%;
  
  /* 字体系统 */
  --font-xs: 12px;
  --font-sm: 14px;
  --font-base: 16px;
  --font-lg: 18px;
  --font-xl: 20px;
  --font-2xl: 24px;
  
  /* 阴影系统 */
  --shadow-sm: 0 1px 2px rgba(0, 0, 0, 0.05);
  --shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  --shadow-lg: 0 10px 15px rgba(0, 0, 0, 0.1);
  --shadow-xl: 0 20px 25px rgba(0, 0, 0, 0.15);
  
  /* 过渡动画 */
  --transition-fast: 0.15s ease;
  --transition: 0.3s ease;
}

/* 全局重置 */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
  font-size: var(--font-base);
  line-height: 1.5;
  color: var(--text-primary);
  background: var(--background-color);
  -webkit-font-smoothing: antialiased;
  -webkit-tap-highlight-color: transparent;
  overflow-x: hidden;
  padding-top: var(--safe-top);
  padding-bottom: calc(var(--bottom-nav-height) + var(--safe-bottom));
}

/* 手机端头部 - 重构为上下两层 */
.header {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  background: linear-gradient(135deg,
    rgba(168, 85, 247, 0.95),
    rgba(184, 61, 186, 0.95)
  );
  -webkit-backdrop-filter: blur(20px);
  backdrop-filter: blur(20px);
  color: var(--text-inverse);
  z-index: 1000;
  box-shadow: var(--shadow-lg);
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  transition: var(--transition);
  display: flex;
  flex-direction: column;
}

/* 上层：Logo区域 */
.header-top {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: var(--space-md) var(--space-lg);
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

/* 下层：搜索和功能区域 */
.header-bottom {
  display: flex;
  align-items: center;
  padding: var(--space-md) var(--space-lg);
  gap: var(--space-md);
}

/* 搜索区域 (70%) */
.search-section {
  flex: 0 0 70%;
}

/* 功能按钮区域 (30%) */
.controls-section {
  flex: 0 0 30%;
  display: flex;
  align-items: center;
  justify-content: flex-end;
  gap: var(--space-sm);
}



.logo-container {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: var(--space-sm);
}

.gmh-logo-img {
  height: 36px;
  width: auto;
  border-radius: var(--radius);
}

.logo-text h1 {
  font-size: var(--font-xl);
  font-weight: 700;
  white-space: nowrap;
  color: white;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

/* 搜索栏 */
.search-container {
  flex: 1;
  position: relative;
  display: flex;
  align-items: center;
  gap: var(--space-sm);
  max-width: 600px;
}

.search-input-wrapper {
  position: relative;
  background: rgba(255, 255, 255, 0.15);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: var(--radius-full);
  display: flex;
  align-items: center;
  padding: 0 var(--space-md);
  flex: 1;
  min-width: 200px;
  transition: all var(--transition-fast);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
}

.search-input-wrapper:focus-within {
  background: rgba(255, 255, 255, 0.25);
  border-color: rgba(255, 255, 255, 0.4);
  box-shadow: 0 0 0 2px rgba(255, 255, 255, 0.1);
  transform: translateY(-1px);
}

.search-input {
  flex: 1;
  background: none;
  border: none;
  color: var(--text-inverse);
  font-size: var(--font-base);
  font-weight: 400;
  padding: var(--space-sm) 0;
  outline: none;
  transition: all var(--transition-fast);
}

.search-input::placeholder {
  color: rgba(255, 255, 255, 0.7);
  font-weight: 400;
}

.search-input:focus {
  color: var(--text-inverse);
}

.gemini-toggle {
  background: rgba(255, 255, 255, 0.2);
  border: none;
  color: white;
  padding: var(--space-xs) var(--space-sm);
  border-radius: var(--radius);
  font-size: var(--font-xs);
  cursor: pointer;
}

/* 手机端底部导航 */
.bottom-nav {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  height: var(--bottom-nav-height);
  background: white;
  display: flex;
  box-shadow: 0 -4px 6px rgba(0, 0, 0, 0.1);
  z-index: 1000;
}

.nav-item {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 2px;
  color: var(--text-secondary);
  font-size: var(--font-xs);
  cursor: pointer;
  transition: var(--transition-fast);
  text-decoration: none;
}

.nav-item.active {
  color: var(--primary-color);
}

.nav-item .icon {
  font-size: var(--font-lg);
}

/* 浮动对话按钮 */
.fab-chat {
  position: fixed;
  bottom: calc(var(--bottom-nav-height) + var(--space-lg));
  right: var(--space-lg);
  width: var(--fab-size);
  height: var(--fab-size);
  background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
  border: none;
  border-radius: var(--radius-full);
  color: white;
  font-size: var(--font-xl);
  box-shadow: var(--shadow-lg);
  cursor: pointer;
  z-index: 1001;
  transition: var(--transition);
}

.fab-chat:hover {
  transform: scale(1.1);
  box-shadow: var(--shadow-xl);
}



@keyframes pulse {
  0%, 100% { transform: scale(1); }
  50% { transform: scale(1.05); }
}

/* 主内容区域 */
.main-content {
  margin-top: var(--header-height);
  padding: var(--space-lg);
  max-width: 100%;
  overflow-x: hidden;
}

/* 问题详情页面玻璃拟态样式 */
#faqContent {
  background: rgba(255, 255, 255, 0.25);
  -webkit-backdrop-filter: blur(20px);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.18);
  border-radius: var(--radius-2xl);
  padding: var(--space-2xl);
  margin: var(--space-lg);
  box-shadow: 0 8px 32px rgba(168, 85, 247, 0.12);
  position: relative;
  overflow: hidden;
}

/* 顶部光线效果 */
#faqContent::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 1px;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
  z-index: 1;
}

/* FAQ卡片 */
.faq-card {
  background: rgba(255, 255, 255, 0.3);
  -webkit-backdrop-filter: blur(15px);
  backdrop-filter: blur(15px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: var(--radius-lg);
  padding: var(--space-lg);
  margin-bottom: var(--space-md);
  box-shadow: var(--shadow-sm);
  transition: var(--transition);
}

.faq-card:hover {
  background: rgba(255, 255, 255, 0.4);
  box-shadow: var(--shadow-md);
  transform: translateY(-2px);
}

/* 面包屑导航玻璃拟态样式 */
.breadcrumb {
  display: flex;
  align-items: center;
  gap: var(--space-sm);
  margin-bottom: var(--space-xl);
  padding: var(--space-md) var(--space-lg);
  background: rgba(255, 255, 255, 0.2);
  -webkit-backdrop-filter: blur(10px);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.15);
  border-radius: var(--radius-full);
  font-size: var(--font-sm);
  position: relative;
  z-index: 2;
}

.breadcrumb-link {
  background: none;
  border: none;
  color: var(--primary-600);
  cursor: pointer;
  font-size: inherit;
  font-weight: 500;
  padding: var(--space-xs) var(--space-sm);
  border-radius: var(--radius);
  transition: all 0.3s ease;
}

.breadcrumb-link:hover {
  background: rgba(168, 85, 247, 0.1);
  color: var(--primary-700);
  transform: translateY(-1px);
}

.breadcrumb-separator {
  color: var(--text-tertiary);
  font-weight: 300;
}

.breadcrumb-current {
  color: var(--text-primary);
  font-weight: 600;
  max-width: 200px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.faq-header {
  display: flex;
  align-items: flex-start;
  gap: var(--space-md);
  margin-bottom: var(--space-2xl);
  padding: var(--space-xl);
  background: rgba(255, 255, 255, 0.15);
  -webkit-backdrop-filter: blur(15px);
  backdrop-filter: blur(15px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: var(--radius-xl);
  position: relative;
  z-index: 2;
}

.faq-id {
  background: linear-gradient(135deg, var(--primary-500), var(--primary-600));
  color: white;
  padding: var(--space-sm) var(--space-lg);
  border-radius: var(--radius-full);
  font-size: var(--font-sm);
  font-weight: 700;
  min-width: 60px;
  text-align: center;
  box-shadow: 0 4px 12px rgba(168, 85, 247, 0.3);
  border: 1px solid rgba(255, 255, 255, 0.2);
  position: relative;
  z-index: 2;
  flex-shrink: 0;
}

.faq-title {
  font-size: var(--font-2xl);
  font-weight: 700;
  color: var(--text-primary);
  line-height: 1.3;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  flex: 1;
  position: relative;
  z-index: 2;
}

.faq-priority {
  display: inline-block;
  padding: var(--space-xs) var(--space-md);
  border-radius: var(--radius-full);
  font-size: var(--font-xs);
  font-weight: 600;
  margin-left: var(--space-sm);
  position: relative;
  z-index: 2;
}

.priority-high {
  background: rgba(239, 68, 68, 0.15);
  color: var(--danger-color);
  border: 1px solid rgba(239, 68, 68, 0.3);
}

.priority-medium {
  background: rgba(245, 158, 11, 0.15);
  color: var(--warning-color);
  border: 1px solid rgba(245, 158, 11, 0.3);
}

.priority-low {
  background: rgba(168, 85, 247, 0.15);
  color: var(--primary-600);
  border: 1px solid rgba(168, 85, 247, 0.3);
}

/* FAQ内容区域玻璃拟态样式 */
.faq-body {
  background: rgba(255, 255, 255, 0.1);
  -webkit-backdrop-filter: blur(10px);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: var(--radius-lg);
  padding: var(--space-2xl);
  margin: var(--space-xl) 0;
  font-size: var(--font-base);
  line-height: 1.7;
  color: var(--text-primary);
  position: relative;
  z-index: 2;
}

.faq-body h3 {
  font-size: var(--font-xl);
  font-weight: 700;
  color: var(--primary-700);
  margin: var(--space-2xl) 0 var(--space-lg) 0;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.faq-body h4 {
  font-size: var(--font-lg);
  font-weight: 600;
  color: var(--text-primary);
  margin: var(--space-xl) 0 var(--space-md) 0;
}

.faq-body table {
  width: 100%;
  border-collapse: collapse;
  margin: var(--space-xl) 0;
  background: rgba(255, 255, 255, 0.1);
  -webkit-backdrop-filter: blur(5px);
  backdrop-filter: blur(5px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: var(--radius-lg);
  overflow: hidden;
  box-shadow: var(--shadow-sm);
}

.faq-body th,
.faq-body td {
  padding: var(--space-md);
  text-align: left;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.faq-body th {
  background: rgba(168, 85, 247, 0.1);
  font-weight: 600;
  color: var(--primary-700);
}

.faq-body tr:last-child td {
  border-bottom: none;
}

.faq-summary {
  color: var(--text-secondary);
  line-height: 1.5;
  margin-bottom: var(--space-sm);
}

.faq-meta {
  display: flex;
  align-items: center;
  gap: var(--space-sm);
  font-size: var(--font-xs);
  color: var(--text-secondary);
}

/* 对话界面 */
.chat-container {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: white;
  z-index: 2000;
  display: none;
  flex-direction: column;
}

.chat-container.active {
  display: flex;
}

.chat-header {
  background: var(--primary-color);
  color: white;
  padding: var(--space-lg);
  display: flex;
  align-items: center;
  gap: var(--space-md);
}

.chat-back {
  background: none;
  border: none;
  color: white;
  font-size: var(--font-xl);
  cursor: pointer;
}

.chat-messages {
  flex: 1;
  padding: var(--space-lg);
  overflow-y: auto;
}

.chat-input-container {
  padding: var(--space-lg);
  border-top: 1px solid var(--border-color);
  display: flex;
  gap: var(--space-sm);
}

/* 功能按钮区域样式 */
.header-controls {
  display: flex;
  align-items: center;
  gap: var(--space-sm);
  margin-left: var(--space-sm);
}

/* 主题切换按钮 */
.theme-toggle {
  background: rgba(255, 255, 255, 0.15);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: var(--radius-md);
  padding: var(--space-xs);
  cursor: pointer;
  transition: all var(--transition);
  display: flex;
  align-items: center;
  justify-content: center;
  width: var(--touch-target);
  height: var(--touch-target);
  font-size: 1.2rem;
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  position: relative;
  overflow: hidden;
}

.theme-toggle::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s ease;
}

.theme-toggle:hover::before {
  left: 100%;
}

.theme-toggle:hover {
  background: rgba(255, 255, 255, 0.25);
  border-color: rgba(255, 255, 255, 0.4);
  transform: translateY(-2px);
  box-shadow: var(--shadow-md);
}

.theme-toggle:active {
  transform: translateY(0);
}

/* 语言切换器 */
.language-switcher {
  display: flex;
  background: rgba(255, 255, 255, 0.15);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: var(--radius-md);
  overflow: hidden;
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  box-shadow: var(--shadow-sm);
}

.lang-btn {
  background: transparent;
  border: none;
  padding: var(--space-xs) var(--space-sm);
  cursor: pointer;
  transition: all var(--transition);
  font-size: var(--font-sm);
  font-weight: 500;
  color: rgba(255, 255, 255, 0.8);
  min-width: 36px;
  height: var(--touch-target);
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  overflow: hidden;
}

.lang-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(255, 255, 255, 0.1);
  opacity: 0;
  transition: opacity var(--transition-fast);
}

.lang-btn:hover::before {
  opacity: 1;
}

.lang-btn:hover {
  color: var(--text-inverse);
  transform: translateY(-1px);
}

.lang-btn.active {
  background: rgba(255, 255, 255, 0.25);
  color: var(--text-inverse);
  font-weight: 600;
  box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.1);
}

.lang-btn + .lang-btn {
  border-left: 1px solid rgba(255, 255, 255, 0.2);
}

.chat-input {
  flex: 1;
  border: 1px solid var(--border-color);
  border-radius: var(--radius-lg);
  padding: var(--space-sm) var(--space-md);
  font-size: var(--font-base);
  outline: none;
}

.chat-send {
  background: var(--primary-color);
  color: white;
  border: none;
  border-radius: var(--radius);
  padding: var(--space-sm) var(--space-lg);
  cursor: pointer;
}

/* 响应式调整 */
@media (max-width: 375px) {
  :root {
    --space-lg: 12px;
    --font-base: 14px;
  }

  .header {
    padding: 0 var(--space-md);
  }

  .logo-text h1 {
    font-size: var(--font-base);
  }

  .fab-chat {
    bottom: calc(var(--bottom-nav-height) + var(--space-md));
    right: var(--space-md);
  }

  /* 小屏幕功能按钮优化 */
  .header-controls {
    gap: var(--space-xs);
  }

  .theme-toggle {
    width: 36px;
    height: 36px;
    font-size: 1rem;
  }

  .lang-btn {
    min-width: 28px;
    height: 36px;
    font-size: var(--font-xs);
    padding: var(--space-xs);
  }

  /* 分类网格小屏优化 */
  .category-grid {
    grid-template-columns: repeat(auto-fit, minmax(140px, 1fr));
    gap: var(--space-md);
    padding: var(--space-sm);
  }

  .category-card {
    min-height: 100px;
    padding: var(--space-md);
  }

  .category-icon {
    font-size: 1.5rem;
  }

  .welcome-header h2 {
    font-size: 1.5rem;
  }
}

/* 暗色主题样式 */
.dark-theme {
  /* 暗色主题文本颜色 */
  --text-primary: #f9fafb;
  --text-secondary: #d1d5db;
  --text-tertiary: #9ca3af;
  --text-inverse: #111827;

  /* 暗色主题背景颜色 */
  --background-color: #0f172a;
  --background-secondary: #1e293b;
  --surface-color: #1e293b;
  --surface-elevated: #334155;

  /* 暗色主题边框和阴影 */
  --border-color: #475569;
  --border-light: #64748b;
  --shadow-color: rgba(0, 0, 0, 0.3);
  --overlay-color: rgba(0, 0, 0, 0.7);

  /* 暗色主题品牌色调整 */
  --primary-light: rgba(168, 85, 247, 0.15);

  /* 暗色主题状态颜色 */
  --success-light: rgba(16, 185, 129, 0.15);
  --warning-light: rgba(245, 158, 11, 0.15);
  --danger-light: rgba(239, 68, 68, 0.15);
  --info-light: rgba(59, 130, 246, 0.15);
}

/* 暗色主题下的玻璃拟态分类卡片 */
.dark-theme .category-card {
  background: rgba(30, 41, 59, 0.4);
  border-color: rgba(148, 163, 184, 0.18);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
}

.dark-theme .category-card:hover {
  background: rgba(30, 41, 59, 0.6);
  border-color: rgba(168, 85, 247, 0.4);
  box-shadow: 0 12px 40px rgba(168, 85, 247, 0.25);
}

.dark-theme .category-card::before {
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
}

.dark-theme .category-card .category-icon {
  color: var(--primary-400);
}

.dark-theme .category-card .category-count {
  background: rgba(168, 85, 247, 0.9);
  border-color: rgba(255, 255, 255, 0.1);
}

/* 暗色主题下的问题详情页面 */
.dark-theme #faqContent {
  background: rgba(30, 41, 59, 0.4);
  border-color: rgba(148, 163, 184, 0.18);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
}

.dark-theme #faqContent::before {
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
}

.dark-theme .breadcrumb {
  background: rgba(30, 41, 59, 0.3);
  border-color: rgba(148, 163, 184, 0.15);
}

.dark-theme .faq-header {
  background: rgba(30, 41, 59, 0.2);
  border-color: rgba(148, 163, 184, 0.1);
}

.dark-theme .faq-body {
  background: rgba(30, 41, 59, 0.15);
  border-color: rgba(148, 163, 184, 0.1);
}

.dark-theme .faq-body table {
  background: rgba(30, 41, 59, 0.2);
  border-color: rgba(148, 163, 184, 0.2);
}

.dark-theme .faq-body th {
  background: rgba(168, 85, 247, 0.2);
}

.dark-theme .related-questions {
  background: rgba(30, 41, 59, 0.2);
  border-color: rgba(148, 163, 184, 0.1);
}

.dark-theme .related-item {
  background: rgba(30, 41, 59, 0.15);
  border-color: rgba(148, 163, 184, 0.1);
}

.dark-theme .related-item:hover {
  background: rgba(168, 85, 247, 0.2);
  border-color: rgba(168, 85, 247, 0.4);
}

/* 暗色模式自动检测支持 */
@media (prefers-color-scheme: dark) {
  :root:not(.dark-theme) {
    --text-primary: #f9fafb;
    --text-secondary: #d1d5db;
    --background-color: #111827;
    --surface-color: #1f2937;
    --border-color: #374151;
    --border-light: #4b5563;
  }
}

/* 欢迎页面样式 */
.welcome-content {
  padding: var(--space-lg);
  min-height: calc(100vh - var(--header-height) - var(--bottom-nav-height));
}

.welcome-header {
  text-align: center;
  margin-bottom: var(--space-2xl);
  padding: var(--space-lg) 0;
}

.welcome-header h2 {
  background: linear-gradient(135deg, var(--primary-color), var(--accent-color));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  font-size: 1.75rem;
  font-weight: 700;
  margin-bottom: var(--space-sm);
  line-height: 1.2;
}

.welcome-header p {
  color: var(--text-secondary);
  font-size: var(--font-base);
  max-width: 300px;
  margin: 0 auto;
  line-height: 1.5;
}

/* 分类页面样式 */
.categories-page {
  padding: var(--space-lg);
  min-height: calc(100vh - var(--header-height) - var(--bottom-nav-height));
}

.page-title h2 {
  text-align: center;
  color: var(--primary-color);
  margin-bottom: var(--space-2xl);
  font-size: 1.5rem;
  font-weight: 600;
}

/* 欢迎页面分类网格 */
.category-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(160px, 1fr));
  gap: var(--space-lg);
  padding: var(--space-md);
}

/* 玻璃拟态分类卡片 */
.category-card {
  background: rgba(255, 255, 255, 0.25);
  -webkit-backdrop-filter: blur(20px);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.18);
  border-radius: var(--radius-2xl);
  padding: var(--space-xl);
  cursor: pointer;
  transition: all 0.4s ease;
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  gap: var(--space-md);
  min-height: 140px;
  position: relative;
  overflow: hidden;
  animation: fadeInUp 0.6s ease-out forwards;
  opacity: 0;
  transform: translateY(20px);
  box-shadow: 0 8px 32px rgba(168, 85, 247, 0.12);
}

/* 顶部光线效果 */
.category-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 1px;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
  z-index: 1;
}

.category-card:hover {
  background: rgba(255, 255, 255, 0.35);
  border-color: rgba(168, 85, 247, 0.3);
  transform: translateY(-4px) scale(1.02);
  box-shadow: 0 12px 40px rgba(168, 85, 247, 0.2);
}

.category-card:active {
  transform: translateY(-2px) scale(1.01);
  transition: all 0.1s ease;
}

.category-icon {
  font-size: 2.8rem;
  color: var(--primary-600);
  filter: drop-shadow(0 4px 8px rgba(168, 85, 247, 0.3));
  transition: all 0.3s ease;
  position: relative;
  z-index: 2;
}

.category-card:hover .category-icon {
  transform: scale(1.1) rotate(10deg);
  color: var(--primary-700);
}

.category-name {
  font-weight: 700;
  color: var(--text-primary);
  font-size: var(--font-lg);
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  position: relative;
  z-index: 2;
  transition: all 0.3s ease;
  line-height: 1.2;
}

.category-count {
  font-size: var(--font-sm);
  font-weight: 600;
  color: white;
  background: rgba(168, 85, 247, 0.8);
  -webkit-backdrop-filter: blur(10px);
  backdrop-filter: blur(10px);
  padding: var(--space-xs) var(--space-md);
  border-radius: var(--radius-full);
  border: 1px solid rgba(255, 255, 255, 0.2);
  position: relative;
  z-index: 2;
  transition: all 0.3s ease;
}

/* 分类卡片延迟动画 */
.category-card:nth-child(1) { animation-delay: 0.1s; }
.category-card:nth-child(2) { animation-delay: 0.2s; }
.category-card:nth-child(3) { animation-delay: 0.3s; }
.category-card:nth-child(4) { animation-delay: 0.4s; }
.category-card:nth-child(5) { animation-delay: 0.5s; }
.category-card:nth-child(6) { animation-delay: 0.6s; }

/* 动画关键帧 */
@keyframes fadeInUp {
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.categories-container {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: var(--space-lg);
  padding: 0 var(--space-sm);
}

.category-btn {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: var(--space-2xl) var(--space-lg);
  border: 2px solid var(--border-color);
  border-radius: var(--radius-lg);
  background: var(--surface-color);
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 2px 4px rgba(0,0,0,0.05);
  min-height: var(--touch-target);
  position: relative;
  overflow: hidden;
}

.category-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(217, 70, 239, 0.1), transparent);
  transition: left 0.5s;
}

.category-btn:hover::before {
  left: 100%;
}

.category-btn:hover {
  border-color: var(--primary-color);
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(217, 70, 239, 0.2);
}

.category-btn:active {
  transform: translateY(0);
  transition: transform 0.1s;
}

.category-btn .category-icon {
  font-size: 2rem;
  margin-bottom: var(--space-sm);
  display: block;
}

.category-btn .category-name {
  font-size: 0.9rem;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: var(--space-xs);
  text-align: center;
}

.category-btn .category-count {
  font-size: 0.8rem;
  color: var(--text-secondary);
  background: var(--border-light);
  padding: 0.2rem 0.5rem;
  border-radius: var(--radius-full);
  min-width: 20px;
  text-align: center;
}

/* 分类问题页面样式 */
.category-questions-page {
  padding: var(--space-lg);
  min-height: calc(100vh - var(--header-height) - var(--bottom-nav-height));
}

.page-header {
  display: flex;
  align-items: center;
  margin-bottom: var(--space-2xl);
  padding-bottom: var(--space-lg);
  border-bottom: 1px solid var(--border-color);
}

.back-btn {
  background: none;
  border: none;
  color: var(--primary-color);
  font-size: 1rem;
  cursor: pointer;
  margin-right: var(--space-lg);
  padding: var(--space-sm);
  border-radius: var(--radius);
  transition: background-color 0.2s;
  min-height: var(--touch-target);
  display: flex;
  align-items: center;
}

.back-btn:hover {
  background-color: var(--primary-light);
}

.page-header h2 {
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--text-primary);
  margin: 0;
}

.questions-list {
  display: flex;
  flex-direction: column;
  gap: var(--space-sm);
}

.question-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--space-lg);
  background: var(--surface-color);
  border: 1px solid var(--border-color);
  border-radius: var(--radius);
  cursor: pointer;
  transition: all 0.2s ease;
  min-height: var(--touch-target);
}

.question-item:hover {
  background: var(--primary-light);
  border-color: var(--primary-color);
  transform: translateX(4px);
}

.question-item:active {
  transform: translateX(2px);
  transition: transform 0.1s;
}

.question-text {
  flex: 1;
  font-size: 0.9rem;
  color: var(--text-primary);
  line-height: 1.4;
  padding-right: var(--space-md);
}

.question-arrow {
  color: var(--text-secondary);
  font-size: 1.2rem;
  transition: transform 0.2s;
}

.question-item:hover .question-arrow {
  transform: translateX(4px);
  color: var(--primary-color);
}

/* 页面隐藏状态 */
.page-hidden {
  display: none !important;
}

/* 流畅动画 */
.faq-card {
  animation: slideInUp 0.3s ease;
}

@keyframes slideInUp {
  from {
    transform: translateY(20px);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

/* 分类按钮动画 */
.category-btn {
  animation: fadeInScale 0.4s ease forwards;
}

@keyframes fadeInScale {
  from {
    opacity: 0;
    transform: scale(0.9);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

/* 相关问题区域玻璃拟态样式 */
.related-questions {
  background: rgba(255, 255, 255, 0.15);
  -webkit-backdrop-filter: blur(15px);
  backdrop-filter: blur(15px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: var(--radius-xl);
  padding: var(--space-2xl);
  margin-top: var(--space-2xl);
  position: relative;
  z-index: 2;
}

.related-questions h3 {
  font-size: var(--font-xl);
  font-weight: 700;
  color: var(--primary-700);
  margin-bottom: var(--space-lg);
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.related-list {
  display: flex;
  flex-direction: column;
  gap: var(--space-sm);
}

.related-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: var(--space-lg);
  background: rgba(255, 255, 255, 0.1);
  -webkit-backdrop-filter: blur(10px);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: var(--radius-lg);
  cursor: pointer;
  transition: all 0.3s ease;
  text-decoration: none;
  color: var(--text-primary);
}

.related-item:hover {
  background: rgba(168, 85, 247, 0.15);
  border-color: rgba(168, 85, 247, 0.3);
  transform: translateY(-2px);
  box-shadow: var(--shadow-md);
}

.related-title {
  flex: 1;
  font-weight: 500;
  line-height: 1.4;
}

.related-arrow {
  color: var(--primary-600);
  font-size: var(--font-lg);
  font-weight: 600;
  transition: transform 0.3s ease;
}

.related-item:hover .related-arrow {
  transform: translateX(4px);
  color: var(--primary-700);
}

/* 问题项动画 */
.question-item {
  animation: slideInLeft 0.3s ease forwards;
}

@keyframes slideInLeft {
  from {
    opacity: 0;
    transform: translateX(-20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}